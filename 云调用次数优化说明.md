# 微信云开发调用次数优化说明

## 🔥 问题发现

用户反馈：体验版上传后，每5分钟有60-70次的云开发调用，即使没人玩游戏时也持续发生。

## 📊 调用次数构成分析

微信云开发的20万调用次数额度包括：
1. **云函数调用次数**
2. **数据库操作次数**（增删改查每次都算）
3. **云存储操作次数**
4. **其他云开发API调用**

## 🔍 问题根源分析

### 主要问题：循环调用导致的频繁数据库操作

#### 问题流程：
1. **游戏初始化** → `GameDataManager.initialize()` → `setupAutoSync()`
2. **数据同步** → `loadCloudDataToLocal()` 调用 `GameData.setCoin()` 和 `GameData.setBestScore()`
3. **自动同步触发** → `setupAutoSync()` 重写的方法每次调用都触发 `syncPlayerDataToCloud()`
4. **大量数据库操作** → 每次同步包含多次数据库读写

#### 每次 `syncPlayerDataToCloud()` 的操作：
- `getWxContext()` → 调用 `login` 云函数 → **1次云函数调用**
- `getCurrentUserDataInternal()` → 查询用户数据 → **1次数据库查询**
- `createOrUpdateUserData()` → 更新用户数据 → **1次数据库更新**

**总计：每次同步 = 3次调用**

#### 循环调用计算：
- 如果有6个关卡分数需要同步：`6 × 3 = 18次调用`
- 加上金币同步：`1 × 3 = 3次调用`
- **初始化总计：21次调用**

### 次要问题：定时云函数
- `updateLeaderboardCache` 每分钟执行1次
- 每次执行包含6个关卡的排行榜计算
- 每个关卡涉及聚合查询和缓存更新

## ✅ 解决方案

### 1. 修复循环调用问题

#### 在 `GameDataManager.ts` 中：
```typescript
// 添加自动同步开关
private autoSyncEnabled: boolean = true;

// 修改setupAutoSync方法，移除addCoin的自动同步
public setupAutoSync(): void {
    // 只有setCoin和setBestScore会触发自动同步
    // addCoin不会触发，避免游戏中频繁同步
    const originalSetCoin = GameData.setCoin;
    GameData.setCoin = (coins: number) => {
        originalSetCoin.call(GameData, coins);
        if (this.autoSyncEnabled) {
            this.syncPlayerDataToCloud();
        }
    };
    // 注意：不监听addCoin，避免游戏中频繁调用
}

// 添加控制方法
public setAutoSyncEnabled(enabled: boolean): void {
    this.autoSyncEnabled = enabled;
}
```

#### 在 `loadCloudDataToLocal()` 中：
```typescript
// 临时禁用自动同步，避免循环调用
const originalAutoSync = this.autoSyncEnabled;
this.setAutoSyncEnabled(false);

// 执行数据加载...

// 恢复自动同步设置
this.setAutoSyncEnabled(originalAutoSync);
```

### 2. 实现游戏结束时统一同步

#### 添加游戏结束同步方法：
```typescript
// 游戏结束时同步数据（推荐调用时机）
public async syncGameEndData(gameMode: GameMode, finalScore: number): Promise<boolean> {
    // 1. 立即同步分数（如果是新纪录）
    if (finalScore > GameData.getBestScore(gameMode)) {
        await this.syncScoreImmediately(gameMode, finalScore);
    }

    // 2. 立即同步总金币
    const totalCoins = GameData.getCoin();
    await this.syncCoinsImmediately(totalCoins, 'set');

    return true;
}
```

#### 在 `GameOverUI.ts` 中调用：
```typescript
// 游戏结束时同步数据到云端
private async syncGameEndData(gameMode: GameMode, finalScore: number): Promise<void> {
    const gameDataManager = GameDataManager.instance;
    if (gameDataManager) {
        await gameDataManager.syncGameEndData(gameMode, finalScore);
    }
}
```

### 3. 添加缺失的方法

在 `GameData.ts` 中添加：
```typescript
// 设置指定模式的最高分（云数据库同步用）
public static setBestScore(mode: GameMode, score: number): void {
    const key = this.BESTSCORE_PREFIX + mode;
    localStorage.setItem(key, score.toString());
    console.log(`设置${this.getGameModeName(mode)}最高分为: ${score}`);
}
```

## 📈 优化效果预期

### 修复前：
- **初始化时**：21次调用（循环调用）
- **游戏进行中**：每收集1个金币 = 3次调用（频繁！）
- **定时云函数**：每分钟13次调用（1次云函数 + 12次数据库操作）

### 修复后：
- **初始化时**：3次调用（仅一次同步）
- **游戏进行中**：0次调用（完全不同步）
- **游戏结束时**：最多6次调用（分数3次 + 金币3次，仅在有新纪录时）
- **定时云函数**：每5分钟13次调用（优化频率）

### 优化效果：
- **初始化优化**：从21次减少到3次（减少85%）
- **游戏中优化**：从频繁调用减少到0次调用（减少100%）
- **定时任务优化**：从每分钟13次减少到每5分钟13次（减少80%）
- **整体优化**：大幅减少不必要的云开发调用，预计减少90%以上的调用量

## ⚙️ 定时云函数配置说明

### Cron表达式格式：
- **修改前**：`"0 */1 * * * * *"` - 每分钟执行一次
- **修改后**：`"0 */5 * * * *"` - 每5分钟执行一次

### Cron表达式说明：
```
秒 分 时 日 月 周 年
0 */5 * * * *
│  │  │ │ │ │
│  │  │ │ │ └─ 周几 (0-7, 0和7都表示周日)
│  │  │ │ └─── 月份 (1-12)
│  │  │ └───── 日期 (1-31)
│  │  └─────── 小时 (0-23)
│  └───────── 分钟 (0-59)
└─────────── 秒 (0-59)
```

## 🔧 进一步优化建议

1. **批量同步**：将多个数据变化合并为一次同步请求
2. **缓存策略**：增加本地缓存，减少重复查询
3. **条件同步**：只在数据真正变化时才同步
4. ✅ **定时优化**：已将排行榜更新频率从每分钟改为每5分钟

## 🎯 总结

主要问题是初始化时的循环调用导致的频繁数据库操作。通过添加自动同步开关和临时禁用机制，可以有效解决这个问题，大幅减少不必要的云开发调用次数。
