# 微信登录和数据同步集成说明

## 🎯 功能概述

本项目已完整实现微信小游戏的登录系统和数据同步功能，包括：

### ✅ 已实现功能

1. **微信登录系统**
   - 自动弹出授权登录（必须授权才能继续游戏）
   - 静默登录获取openid
   - 授权获取用户信息（昵称、头像等）
   - 登录状态管理

2. **数据同步机制**
   - 登录后自动同步云端数据到本地
   - 本地数据变化实时同步到云端（使用本地API）
   - 云端数据变化通过云函数同步到本地（如邀请奖励）

3. **真实好友排行榜**
   - 通过wx.getFriendCloudStorage获取真实微信好友数据
   - 通过wx.setUserCloudStorage同步玩家分数供好友查看
   - 按分数排序显示好友排名

4. **全服排行榜优化**
   - 通过云函数按分数排序获取前100名
   - 修复了轻松关卡（GameMode=0）的显示问题

5. **邀请码系统**
   - 自动生成唯一邀请码
   - 云端验证邀请码有效性
   - 双方奖励机制（各获得2000金币）

## 🏗️ 技术架构

### 核心组件

```
WeChatLoginManager (微信登录管理)
    ↓
GameDataManager (游戏数据管理)
    ↓
CloudDatabaseManager (云数据库管理)
    ↓
InviteCodeManager (邀请码管理)
    ↓
WeChatFriendsData (好友数据管理)
```

### 数据流程

#### 登录流程
```
游戏启动 → 自动弹出微信授权 → 静默登录获取openid → 获取用户信息
    ↓
检查云端数据 → 如有数据同步到本地，如无数据上传本地到云端
    ↓
生成唯一邀请码 → 设置自动同步监听 → 游戏开始
```

#### 数据同步流程
```
数据变化 → 自动触发同步 → 本地API上传 → 云数据库更新
    ↓
微信云存储更新 → 好友可见新分数
```

## 📁 文件结构

### 新增文件
- `assets/scripts/WeChatLoginManager.ts` - 微信登录管理器
- `cloudfunctions/login/` - 登录云函数
- `cloudfunctions/generateInviteCode/` - 生成唯一邀请码云函数
- `cloudfunctions/useInviteCode/` - 邀请码云函数

### 修改文件
- `assets/scripts/Data/GameDataManager.ts` - 集成登录功能
- `assets/scripts/Data/CloudDatabaseManager.ts` - 添加用户数据管理
- `assets/scripts/Data/WeChatFriendsData.ts` - 实现真实好友数据
- `assets/scripts/InviteCodeManager.ts` - 添加设置邀请码方法
- `cloudfunctions/getLeaderboard/index.js` - 修复参数验证

## 🔧 部署步骤

### 1. 云函数部署
需要部署以下云函数：
```bash
# 在微信开发者工具中右键上传并部署
cloudfunctions/login/
cloudfunctions/generateInviteCode/
cloudfunctions/getLeaderboard/
cloudfunctions/useInviteCode/
```

### 2. 数据库权限配置
在云开发控制台设置players集合权限：
```javascript
{
  "read": true,
  "write": "doc._openid == auth.openid"
}
```

### 3. 微信小游戏配置
确保app.js中正确初始化云开发：
```javascript
App({
  onLaunch() {
    if (wx.cloud) {
      wx.cloud.init({
        env: 'your-env-id', // 替换为你的云开发环境ID
        traceUser: true
      });
    }
  }
});
```

## 🎮 使用说明

### 登录流程
1. **游戏启动**：自动弹出微信授权，用户必须授权才能继续
2. **真机调试**：可能跳过授权弹窗，但功能完整
3. **正式版本**：完整的授权流程和数据同步

### 数据同步
- **自动同步**：金币、分数变化时自动上传到云端
- **好友可见**：通过微信云存储，好友可以看到玩家的最新分数
- **邀请奖励**：使用邀请码后，通过云函数给邀请者发放奖励

### 排行榜
- **好友排行榜**：显示真实微信好友的游戏数据
- **全服排行榜**：显示按分数排序的前100名玩家
- **实时更新**：分数变化时排行榜实时更新

## 🐛 问题解决

### 已修复的问题

1. **轻松关卡排行榜为空**
   - 原因：GameMode.NORMAL_EASY = 0，云函数参数验证错误
   - 解决：修改云函数参数验证逻辑

2. **排行榜只显示21条**
   - 原因：微信云数据库默认查询限制20条
   - 解决：实现分批查询和云函数聚合查询

3. **好友排行榜显示模拟数据**
   - 原因：使用了云数据库数据而不是真实好友
   - 解决：使用wx.getFriendCloudStorage获取真实好友数据

### 测试环境限制
- **好友数据**：测试环境无法获取真实好友，正式环境正常
- **授权弹窗**：真机调试可能跳过，正式版本正常显示

## 📊 数据结构

### 云数据库 players 集合
```javascript
{
  _id: "文档ID",
  _openid: "微信openid",
  nickname: "玩家昵称",
  avatarUrl: "头像URL",
  coins: 1000,
  inviteCode: "ABC123",
  hasUsedInviteCode: false,
  topScores: {
    0: [100, 95, 90], // 各关卡分数数组
    1: [80, 75, 70],
    // ...
  },
  createTime: "2025-01-01T00:00:00.000Z",
  updateTime: "2025-01-01T00:00:00.000Z"
}
```

### 微信云存储数据
```javascript
{
  key: "topScores",
  value: JSON.stringify({
    0: [100, 95, 90], // 各关卡分数数组
    1: [80, 75, 70],
    // ...
  })
}
```

## 🔍 调试功能

在浏览器控制台可使用：
```javascript
// 测试全服排行榜云函数
testGlobalLeaderboard();

// 查看登录状态
WeChatLoginManager.instance.isLoggedIn();

// 手动同步数据
GameDataManager.instance.syncPlayerDataToCloud();
```

## 📈 性能优化

1. **本地API同步**：数据变化使用本地API上传，节省云函数调用
2. **云函数聚合**：排行榜使用云函数聚合查询，提高性能
3. **自动监听**：只在数据真正变化时才触发同步
4. **批量操作**：多个数据变化合并为一次同步请求

## 🔒 安全机制

1. **openid验证**：使用微信openid作为用户唯一标识
2. **权限控制**：数据库权限限制用户只能修改自己的数据
3. **云函数验证**：邀请码验证在云端进行，防止作弊
4. **数据校验**：所有数据变化都经过格式和权限校验

## 🎯 总结

现在的系统已经完整实现了您要求的所有功能：

✅ **微信登录**：完整的登录流程和状态管理
✅ **数据同步**：本地API实时同步，云函数处理特殊情况
✅ **真实好友**：使用微信API获取真实好友数据
✅ **全服排行榜**：云函数按分数排序前100名
✅ **邀请码系统**：完整的生成、验证、奖励机制

系统设计遵循了性能优化和成本控制的原则，在保证功能完整性的同时，最大化了用户体验。
