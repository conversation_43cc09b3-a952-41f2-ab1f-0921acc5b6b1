import { _decorator, Component, Node, Button, ScrollView, Prefab, instantiate, Label, Sprite, SpriteFrame, Color, UITransform, Layout, EventTouch, Vec3, ScrollBar } from 'cc';
import { GameMode } from '../GameData';
import { WeChatFriendsData, RankItem } from '../Data/WeChatFriendsData';
import { CustomScrollBar } from './CustomScrollBar';
const { ccclass, property } = _decorator;

/**
 * 微信好友排行榜UI管理器
 */
@ccclass('FriendsRankUI')
export class FriendsRankUI extends Component {
    
    // 关卡按钮容器
    @property(Node)
    levelButtonsContainer: Node = null;

    // 关卡按钮
    @property(Button)
    easyBtn: Button = null;

    @property(Button)
    normalBtn: Button = null;

    @property(Button)
    hardBtn: Button = null;

    @property(Button)
    windBtn: Button = null;

    @property(Button)
    fogBtn: Button = null;

    @property(Button)
    snowBtn: Button = null;

    // 排行榜滚动视图
    @property(ScrollView)
    friendsRankScrollView: ScrollView = null;

    // 排行榜条目预制体
    @property(Prefab)
    friendsRankItemPrefab: Prefab = null;

    // 默认头像
    @property(SpriteFrame)
    defaultAvatarFrame: SpriteFrame = null;

    // 自定义滚动条（可选，如果设置了就使用自定义滚动条）
    @property(CustomScrollBar)
    customScrollBar: CustomScrollBar = null;

    // 当前选中的关卡
    private currentGameMode: GameMode = GameMode.NORMAL_EASY;

    // 按钮颜色配置
    private readonly SELECTED_COLOR = new Color(100, 200, 100, 255);   // 选中状态：绿色
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255);     // 普通状态：白色

    // 滚动条拖拽相关
    private isDraggingScrollBar: boolean = false;
    private scrollBarNode: Node = null; // 滚动条节点
    private scrollBarHeight: number = 0; // 滚动条高度
    private lastTouchY: number = 0; // 上次触摸的Y坐标

    onLoad() {
        console.log("FriendsRankUI: 初始化好友排行榜UI");
        this.setupButtonEvents();

        // 根据是否有自定义滚动条来决定使用哪种滚动方式
        if (this.customScrollBar) {
            console.log("FriendsRankUI: 使用自定义滚动条");
        } else {
            console.log("FriendsRankUI: 未设置自定义滚动条，跳过滚动条拖拽功能");
            // 注释掉内置滚动条功能，因为我们主要使用CustomScrollBar
            // this.setupScrollBarDrag();
        }
    }

    start() {
        // 默认显示轻松关卡的排行榜
        this.selectLevel(GameMode.NORMAL_EASY);
    }

    onDestroy() {
        // 移除滚动条事件监听
        if (this.scrollBarNode) {
            this.scrollBarNode.off(Node.EventType.TOUCH_START, this.onScrollBarTouchStart, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_MOVE, this.onScrollBarTouchMove, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_END, this.onScrollBarTouchEnd, this);
            this.scrollBarNode.off(Node.EventType.TOUCH_CANCEL, this.onScrollBarTouchEnd, this);
        }
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 注意：按钮事件现在通过编辑器配置，这里不需要代码绑定
        // 如果需要代码绑定，使用以下方式：
        /*
        if (this.easyBtn) {
            this.easyBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_EASY), this);
        }
        if (this.normalBtn) {
            this.normalBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_STANDARD), this);
        }
        if (this.hardBtn) {
            this.hardBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.NORMAL_HARD), this);
        }
        if (this.windBtn) {
            this.windBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_WIND), this);
        }
        if (this.fogBtn) {
            this.fogBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_FOG), this);
        }
        if (this.snowBtn) {
            this.snowBtn.node.on(Button.EventType.CLICK, () => this.selectLevel(GameMode.CHALLENGE_SNOW), this);
        }
        */
    }

    /**
     * 选择关卡并更新排行榜
     */
    public selectLevel(event?: any, customEventData?: any): void {
        let gameMode: GameMode;

        console.log(`FriendsRankUI: 收到参数 event=${event}, customEventData=${customEventData}, type=${typeof customEventData}`);

        // 处理从编辑器按钮事件传来的参数
        if (customEventData !== undefined && customEventData !== null) {
            // 尝试多种方式解析参数
            let modeValue: number;

            if (typeof customEventData === 'string') {
                modeValue = parseInt(customEventData);
            } else if (typeof customEventData === 'number') {
                modeValue = customEventData;
            } else {
                // 尝试转换为字符串再解析
                modeValue = parseInt(String(customEventData));
            }

            if (!isNaN(modeValue) && modeValue >= 0 && modeValue <= 5) {
                gameMode = modeValue as GameMode;
            } else {
                gameMode = GameMode.NORMAL_EASY;
                console.warn(`FriendsRankUI: 无效的CustomEventData ${customEventData}，使用默认值`);
            }
        } else if (typeof event === 'number') {
            // 直接传入数字
            gameMode = event as GameMode;
        } else {
            // 默认值
            gameMode = GameMode.NORMAL_EASY;
            console.warn(`FriendsRankUI: 无效的参数，使用默认值`);
        }

        console.log(`FriendsRankUI: 选择关卡 ${gameMode} (${this.getGameModeName(gameMode)})`);

        this.currentGameMode = gameMode;
        this.updateButtonStates();
        this.updateRankingList();
    }

    /**
     * 获取游戏模式名称
     */
    private getGameModeName(mode: GameMode): string {
        const names = {
            [GameMode.NORMAL_EASY]: "轻松",
            [GameMode.NORMAL_STANDARD]: "标准",
            [GameMode.NORMAL_HARD]: "困难",
            [GameMode.CHALLENGE_WIND]: "大风吹",
            [GameMode.CHALLENGE_FOG]: "大雾起",
            [GameMode.CHALLENGE_SNOW]: "大雪飘"
        };
        return names[mode] || "未知";
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        const buttons = [
            { btn: this.easyBtn, mode: GameMode.NORMAL_EASY },
            { btn: this.normalBtn, mode: GameMode.NORMAL_STANDARD },
            { btn: this.hardBtn, mode: GameMode.NORMAL_HARD },
            { btn: this.windBtn, mode: GameMode.CHALLENGE_WIND },
            { btn: this.fogBtn, mode: GameMode.CHALLENGE_FOG },
            { btn: this.snowBtn, mode: GameMode.CHALLENGE_SNOW }
        ];

        buttons.forEach(({ btn, mode }) => {
            if (btn) {
                const sprite = btn.getComponent(Sprite);
                if (sprite) {
                    sprite.color = mode === this.currentGameMode ? this.SELECTED_COLOR : this.NORMAL_COLOR;
                }
            }
        });
    }

    /**
     * 更新排行榜列表
     */
    private updateRankingList(): void {
        if (!this.friendsRankScrollView || !this.friendsRankItemPrefab) {
            console.error("FriendsRankUI: ScrollView或ItemPrefab未设置");
            return;
        }

        console.log("FriendsRankUI: 开始更新排行榜列表");

        // 先刷新玩家分数，确保排行榜数据是最新的
        WeChatFriendsData.instance.refreshPlayerScores();

        // 清空现有列表
        const content = this.friendsRankScrollView.content;
        if (content) {
            content.removeAllChildren();
        }

        // 获取排行榜数据
        const rankingData = WeChatFriendsData.instance.getFriendsRanking(this.currentGameMode, 100);

        console.log(`FriendsRankUI: 获取到 ${rankingData.length} 条排行榜数据`);

        // 创建排行榜条目（恢复同步创建，确保数据完整性）
        rankingData.forEach((rankItem) => {
            this.createRankItem(rankItem);
        });

        // 调整content高度以适应所有条目
        this.adjustContentHeight(rankingData.length);

        // 重置滚动位置到顶部
        this.scheduleOnce(() => {
            if (this.friendsRankScrollView) {
                this.friendsRankScrollView.scrollToTop(0.1);
            }

            // 如果使用自定义滚动条，更新把手大小
            if (this.customScrollBar) {
                this.customScrollBar.updateHandleSize();
            }
        }, 0.1);
    }

    /**
     * 创建排行榜条目
     */
    private createRankItem(rankItem: RankItem): void {
        if (!this.friendsRankItemPrefab || !this.friendsRankScrollView) {
            return;
        }

        const itemNode = instantiate(this.friendsRankItemPrefab);
        if (!itemNode) {
            console.error("FriendsRankUI: 无法实例化排行榜条目预制体");
            return;
        }

        // 设置排行榜条目数据
        this.setupRankItemData(itemNode, rankItem);

        // 添加到滚动视图内容
        this.friendsRankScrollView.content.addChild(itemNode);
    }

    /**
     * 设置排行榜条目数据
     */
    private setupRankItemData(itemNode: Node, rankItem: RankItem): void {
        // 设置排名
        const rankLabel = itemNode.getChildByName("RankLabel")?.getComponent(Label);
        if (rankLabel) {
            rankLabel.string = rankItem.rank.toString();
            
            // 前三名使用特殊颜色
            if (rankItem.rank === 1) {
                rankLabel.color = new Color(255, 215, 0, 255); // 金色
            } else if (rankItem.rank === 2) {
                rankLabel.color = new Color(192, 192, 192, 255); // 银色
            } else if (rankItem.rank === 3) {
                rankLabel.color = new Color(205, 127, 50, 255); // 铜色
            } else {
                rankLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置头像（这里使用默认头像，实际项目中应该加载微信头像）
        const avatarSprite = itemNode.getChildByName("AvatarSprite")?.getComponent(Sprite);
        if (avatarSprite && this.defaultAvatarFrame) {
            avatarSprite.spriteFrame = this.defaultAvatarFrame;
        }

        // 设置昵称
        const nameLabel = itemNode.getChildByName("NameLabel")?.getComponent(Label);
        if (nameLabel) {
            nameLabel.string = rankItem.friend.nickname;
            
            // 如果是自己，使用特殊颜色
            if (rankItem.friend.id === "self") {
                nameLabel.color = new Color(100, 255, 100, 255); // 绿色
            } else {
                nameLabel.color = new Color(255, 255, 255, 255); // 白色
            }
        }

        // 设置分数
        const scoreLabel = itemNode.getChildByName("ScoreLabel")?.getComponent(Label);
        if (scoreLabel) {
            scoreLabel.string = rankItem.score.toString();
        }
    }

    /**
     * 刷新排行榜数据
     */
    public async refreshRanking(): Promise<void> {
        console.log("FriendsRankUI: 刷新排行榜数据");
        
        try {
            await WeChatFriendsData.instance.refreshFriendsData();
            this.updateRankingList();
            console.log("FriendsRankUI: 排行榜数据刷新完成");
        } catch (error) {
            console.error("FriendsRankUI: 刷新排行榜数据失败", error);
        }
    }

    /**
     * 获取当前选中的游戏模式
     */
    public getCurrentGameMode(): GameMode {
        return this.currentGameMode;
    }

    /**
     * 调整ScrollView内容高度
     */
    private adjustContentHeight(itemCount: number): void {
        if (!this.friendsRankScrollView || !this.friendsRankScrollView.content) {
            return;
        }

        const content = this.friendsRankScrollView.content;

        // 等待一帧，确保所有条目都已创建完成
        this.scheduleOnce(() => {
            this.calculateAndSetContentHeight(content, itemCount);
        }, 0);
    }

    /**
     * 计算并设置实际的content高度
     */
    private calculateAndSetContentHeight(content: Node, itemCount: number): void {
        if (content.children.length === 0) {
            console.warn("FriendsRankUI: 没有子节点，无法计算高度");
            return;
        }

        // 获取第一个条目的实际高度
        const firstItem = content.children[0];
        const firstItemTransform = firstItem.getComponent(UITransform);
        const actualItemHeight = firstItemTransform?.height || 60;

        // 检查Layout组件的间距设置
        const layout = content.getComponent(Layout);
        const actualSpacing = layout?.spacingY || 0;

        // 计算实际需要的总高度（添加少量缓冲）
        const totalHeight = itemCount * actualItemHeight + (itemCount - 1) * actualSpacing + 20; // 添加20像素缓冲

        // 获取ScrollView的高度
        const scrollViewTransform = this.friendsRankScrollView.node.getComponent(UITransform);
        const scrollViewHeight = scrollViewTransform?.height || 400;

        // 设置content高度：取实际内容高度和ScrollView高度的较大值
        const finalHeight = Math.max(totalHeight, scrollViewHeight);

        // 设置content的高度
        const contentTransform = content.getComponent(UITransform);
        if (contentTransform) {
            contentTransform.height = finalHeight;
            console.log(`FriendsRankUI: 实际条目高度=${actualItemHeight}, 间距=${actualSpacing}, 总高度=${totalHeight}, 最终高度=${finalHeight}`);
        }
    }

    /**
     * 设置滚动条拖拽功能
     */
    private setupScrollBarDrag(): void {
        if (!this.friendsRankScrollView || !this.friendsRankScrollView.verticalScrollBar) {
            console.warn("FriendsRankUI: ScrollView或VerticalScrollBar未设置");
            return;
        }

        // 获取滚动条节点
        this.scrollBarNode = this.friendsRankScrollView.verticalScrollBar.node;
        if (!this.scrollBarNode) {
            console.warn("FriendsRankUI: ScrollBar节点未找到");
            return;
        }

        // 获取滚动条高度
        const scrollBarTransform = this.scrollBarNode.getComponent(UITransform);
        if (scrollBarTransform) {
            this.scrollBarHeight = scrollBarTransform.height;
        } else {
            console.warn("FriendsRankUI: 无法获取ScrollBar的UITransform");
            return;
        }

        // 为滚动条节点添加触摸事件
        this.scrollBarNode.on(Node.EventType.TOUCH_START, this.onScrollBarTouchStart, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_MOVE, this.onScrollBarTouchMove, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_END, this.onScrollBarTouchEnd, this);
        this.scrollBarNode.on(Node.EventType.TOUCH_CANCEL, this.onScrollBarTouchEnd, this);

        console.log(`FriendsRankUI: 滚动条拖拽功能已设置，滚动条高度: ${this.scrollBarHeight}`);
    }

    /**
     * 滚动条触摸开始
     */
    private onScrollBarTouchStart(event: EventTouch): void {
        this.isDraggingScrollBar = true;

        // 记录触摸开始的Y坐标
        this.lastTouchY = event.getUILocation().y;

        // 阻止事件传播到ScrollView
        event.propagationStopped = true;

        console.log("FriendsRankUI: 开始滚动条操作");
    }

    /**
     * 滚动条触摸移动
     */
    private onScrollBarTouchMove(event: EventTouch): void {
        if (!this.isDraggingScrollBar || !this.friendsRankScrollView) {
            return;
        }

        // 阻止事件传播
        event.propagationStopped = true;

        // 获取当前触摸位置
        const currentTouchY = event.getUILocation().y;

        // 计算Y轴移动距离
        const deltaY = currentTouchY - this.lastTouchY;

        // 如果移动距离太小，忽略（避免抖动）
        if (Math.abs(deltaY) < 2) {
            return;
        }

        // 将移动距离转换为滚动进度变化
        // 使用更小的敏感度，让拖拽更精确
        const sensitivity = 0.5; // 调整这个值来控制拖拽敏感度
        const progressDelta = -deltaY / this.scrollBarHeight * sensitivity;

        // 获取当前滚动进度
        const maxScrollOffset = this.friendsRankScrollView.getMaxScrollOffset();
        const currentScrollOffset = this.friendsRankScrollView.getScrollOffset();

        let currentProgress = 0;
        if (maxScrollOffset.y > 0) {
            currentProgress = currentScrollOffset.y / maxScrollOffset.y;
        }

        // 计算新的滚动进度
        let newProgress = currentProgress + progressDelta;
        newProgress = Math.max(0, Math.min(1, newProgress));

        // 应用新的滚动进度
        this.friendsRankScrollView.scrollToPercentVertical(newProgress, 0);

        // 更新上次触摸位置
        this.lastTouchY = currentTouchY;

        // 调试信息（可以注释掉以减少日志输出）
        // console.log(`FriendsRankUI: 滚动进度: ${(newProgress * 100).toFixed(1)}%`);
    }

    /**
     * 滚动条触摸结束
     */
    private onScrollBarTouchEnd(event: EventTouch): void {
        if (!this.isDraggingScrollBar) {
            // 如果不是拖拽状态，说明是点击操作
            this.handleScrollBarClick(event);
        }

        this.isDraggingScrollBar = false;
        this.lastTouchY = 0;

        // 阻止事件传播
        event.propagationStopped = true;

        console.log("FriendsRankUI: 滚动条操作结束");
    }

    /**
     * 处理滚动条点击事件（点击滚动条直接跳转到对应位置）
     */
    private handleScrollBarClick(event: EventTouch): void {
        if (!this.friendsRankScrollView || !this.scrollBarNode) {
            return;
        }

        // 获取滚动条的UITransform
        const scrollBarTransform = this.scrollBarNode.getComponent(UITransform);
        if (!scrollBarTransform) {
            return;
        }

        // 获取点击位置
        const touchPos = event.getUILocation();

        // 将触摸位置转换为滚动条的本地坐标
        const touchLocalPos = new Vec3();
        scrollBarTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0), touchLocalPos);

        // 计算滚动条的范围
        const halfHeight = this.scrollBarHeight / 2;
        const maxY = halfHeight;
        const minY = -halfHeight;

        // 计算滚动进度 (0-1)，注意Y轴方向（上方为0，下方为1）
        let progress = (maxY - touchLocalPos.y) / (maxY - minY);
        progress = Math.max(0, Math.min(1, progress));

        // 设置ScrollView的滚动进度（带动画效果）
        this.friendsRankScrollView.scrollToPercentVertical(progress, 0.3);

        console.log(`FriendsRankUI: 点击滚动条，跳转到进度: ${(progress * 100).toFixed(1)}%`);
    }

    /**
     * 外部调用：显示指定关卡的排行榜
     */
    public showLevelRanking(mode: GameMode): void {
        this.selectLevel(mode);
    }
}
