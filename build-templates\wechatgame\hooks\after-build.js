const fs = require('fs');
const path = require('path');

// 递归复制文件夹
function copyFolderRecursiveSync(source, target) {
    console.log(`[构建钩子] 正在复制: ${source} -> ${target}`);

    if (!fs.existsSync(target)) {
        fs.mkdirSync(target, { recursive: true });
        console.log(`[构建钩子] 创建目标目录: ${target}`);
    }

    if (fs.lstatSync(source).isDirectory()) {
        const files = fs.readdirSync(source);
        console.log(`[构建钩子] 源目录包含文件: ${files.join(', ')}`);

        files.forEach(function (file) {
            const curSource = path.join(source, file);
            const curTarget = path.join(target, file);
            if (fs.lstatSync(curSource).isDirectory()) {
                copyFolderRecursiveSync(curSource, curTarget);
            } else {
                fs.copyFileSync(curSource, curTarget);
                console.log(`[构建钩子] 复制文件: ${curSource} -> ${curTarget}`);
            }
        });
    }
}

module.exports = {
    // 尝试多个可能的钩子函数名称
    onAfterBuild(options, result) {
        this.copyCloudFunctions(options, result);
    },

    onAfterCompressSettings(options, result) {
        this.copyCloudFunctions(options, result);
    },

    copyCloudFunctions(options, result) {
        console.log('[构建钩子] === 构建后钩子开始执行 ===');
        console.log('[构建钩子] 构建选项:', JSON.stringify(options, null, 2));
        console.log('[构建钩子] 构建结果:', JSON.stringify(result, null, 2));

        // 尝试多种可能的项目根路径
        const possibleRoots = [
            options.projectPath,
            process.cwd(),
            path.dirname(result.dest),
            path.join(path.dirname(result.dest), '..')
        ];

        let projectRoot = null;
        let sourcePath = null;

        for (const root of possibleRoots) {
            const testPath = path.join(root, 'cloudfunctions');
            console.log(`[构建钩子] 检查路径: ${testPath}`);
            if (fs.existsSync(testPath)) {
                projectRoot = root;
                sourcePath = testPath;
                console.log(`[构建钩子] ✅ 找到cloudfunctions文件夹: ${sourcePath}`);
                break;
            }
        }

        const buildPath = result.dest;
        const targetPath = path.join(buildPath, 'cloudfunctions');

        console.log(`[构建钩子] 项目根目录: ${projectRoot}`);
        console.log(`[构建钩子] 构建输出目录: ${buildPath}`);
        console.log(`[构建钩子] 源路径: ${sourcePath}`);
        console.log(`[构建钩子] 目标路径: ${targetPath}`);

        if (sourcePath && fs.existsSync(sourcePath)) {
            try {
                copyFolderRecursiveSync(sourcePath, targetPath);
                console.log('[构建钩子] ✅ cloudfunctions文件夹复制成功！');

                // 验证复制结果
                if (fs.existsSync(targetPath)) {
                    const files = fs.readdirSync(targetPath);
                    console.log(`[构建钩子] ✅ 验证成功，目标目录包含: ${files.join(', ')}`);
                } else {
                    console.error('[构建钩子] ❌ 验证失败，目标目录不存在');
                }
            } catch (error) {
                console.error('[构建钩子] ❌ 复制cloudfunctions文件夹失败:', error);
            }
        } else {
            console.warn('[构建钩子] ⚠️ 未找到cloudfunctions文件夹');
            console.log('[构建钩子] 当前工作目录:', process.cwd());
            console.log('[构建钩子] 尝试的路径:', possibleRoots.map(root => path.join(root, 'cloudfunctions')));
        }

        console.log('[构建钩子] === 构建后钩子执行完成 ===');
    }
};
