# 微信云开发数据库集成说明

## 概述

本项目已集成微信云开发数据库，用于存储和管理玩家数据，包括昵称、头像、金币、邀请码、各关卡分数等信息。

## 架构设计

### 🎯 混合架构：本地操作 + 云函数
为了平衡性能和成本，采用混合架构：

#### 本地数据库操作（高频、简单）
- ✅ **个人数据读写**：金币、分数、昵称、头像
- ✅ **数据查询**：个人排名、好友数据
- ✅ **邀请码验证**：查询邀请码是否存在
- ✅ **成本优势**：不消耗云函数调用次数

#### 云函数操作（低频、复杂）
- ✅ **排行榜计算**：云端聚合排序，避免客户端性能问题
- ✅ **邀请者奖励**：跨用户操作，必须云端处理
- ✅ **性能优势**：复杂计算在云端完成

## 主要功能

### 1. 游戏数据管理器 (GameDataManager) 🆕

位置：`assets/scripts/Data/GameDataManager.ts`

**核心功能**：
- 统一管理本地数据与云数据库同步
- 智能延迟同步，减少数据库操作频率
- 本地优先策略，保证游戏流畅性

### 2. 云数据库管理器 (CloudDatabaseManager)

位置：`assets/scripts/Data/CloudDatabaseManager.ts`

**主要功能**：
- 初始化云数据库连接
- 本地数据库操作（分数、金币、资料更新）
- 批量上传模拟玩家数据
- 云函数排行榜调用

### 3. 数据同步管理器 (DataSyncManager)

位置：`assets/scripts/Data/DataSyncManager.ts`

**同步策略**：
- 分数更新：延迟2秒批量同步
- 金币更新：延迟0.5秒批量同步
- 重试机制：失败时最多重试3次

### 4. 好友数据管理 (WeChatFriendsData)

位置：`assets/scripts/Data/WeChatFriendsData.ts`

**更新内容**：
- 从云数据库获取好友和全服玩家数据
- 云数据库不可用时排行榜为空（便于发现问题）
- 异步数据初始化

### 5. 邀请码管理 (InviteCodeManager)

位置：`assets/scripts/InviteCodeManager.ts`

**更新内容**：
- 本地数据库验证邀请码
- 云函数发放邀请者奖励
- 支持异步邀请码使用

## 数据结构

### 玩家数据 (CloudPlayerData)

```typescript
interface CloudPlayerData {
    _id?: string;                    // 云数据库自动生成的ID
    _openid?: string;               // 微信用户openid（自动生成）
    nickname: string;               // 玩家昵称
    avatarUrl: string;              // 头像URL
    coins: number;                  // 金币数量
    inviteCode: string;             // 邀请码
    topScores: {                    // 每个关卡的最高三次分数
        [GameMode.NORMAL_EASY]: number[];
        [GameMode.NORMAL_STANDARD]: number[];
        [GameMode.NORMAL_HARD]: number[];
        [GameMode.CHALLENGE_WIND]: number[];
        [GameMode.CHALLENGE_FOG]: number[];
        [GameMode.CHALLENGE_SNOW]: number[];
    };
    createdAt?: Date;               // 创建时间
    updatedAt?: Date;               // 更新时间
}
```

## 使用方法

### 1. 初始化游戏数据管理器

```typescript
const gameDataManager = GameDataManager.instance;
const success = await gameDataManager.initialize();
```

### 2. 更新游戏分数

```typescript
// 延迟同步分数
gameDataManager.updateScore(GameMode.NORMAL_EASY, 150);

// 游戏结束时立即同步
await gameDataManager.syncScoreImmediately(GameMode.NORMAL_EASY, 150);
```

### 3. 更新金币

```typescript
// 延迟同步金币
gameDataManager.updateCoins(100, 'add');

// 立即同步金币
await gameDataManager.syncCoinsImmediately(100, 'add');
```

### 4. 获取排行榜（云函数）

```typescript
// 获取指定关卡的排行榜
const leaderboard = await gameDataManager.getLeaderboard(GameMode.NORMAL_STANDARD, 50);
```

### 5. 获取个人排名（本地查询）

```typescript
// 获取个人排名
const rankInfo = await gameDataManager.getPlayerRank(GameMode.NORMAL_STANDARD);
console.log(`排名: ${rankInfo.rank}, 分数: ${rankInfo.score}`);
```

### 6. 使用邀请码

```typescript
// 异步使用邀请码（邀请者奖励通过云函数发放）
const success = await InviteCodeManager.useInviteCode("ABC123");
```

## 测试功能

### 云数据库测试组件 (CloudDatabaseTest)

位置：`assets/scripts/CloudDatabaseTest.ts`

提供以下测试功能：
- 初始化云数据库
- 上传模拟数据
- 查询数据
- 清空数据

### 控制台测试函数

在浏览器控制台中可以直接调用：

```javascript
// 完整测试流程
await window.testCloudDatabase();

// 上传模拟数据
await window.uploadMockData();

// 查询数据
await window.queryCloudData();

// 清空数据
await window.clearCloudData();
```

## 云函数

### 1. updateInviterReward

位置：`cloudfunctions/updateInviterReward/`

**功能**：给邀请者发放奖励

**调用频率**：低频（仅在使用邀请码时）

**使用方法**：
```typescript
const result = await wx.cloud.callFunction({
    name: 'updateInviterReward',
    data: {
        inviteCode: 'ABC123',
        reward: 2000
    }
});
```

### 2. getLeaderboard

位置：`cloudfunctions/getLeaderboard/`

**功能**：获取排行榜数据（云端排序）

**调用频率**：中频（查看排行榜时）

**使用方法**：
```typescript
const result = await wx.cloud.callFunction({
    name: 'getLeaderboard',
    data: {
        gameMode: 'normal_standard',
        limit: 50,
        offset: 0
    }
});
```

## 详细配置指南

### 1. 云开发环境配置

环境ID已配置为：`cloud1-9gx9hhw6416d5994`

### 2. 数据库集合创建

#### 步骤：
1. 打开微信开发者工具
2. 点击"云开发"标签
3. 进入"数据库"
4. 点击"添加集合"
5. 集合名称：`players`
6. 点击确定

#### 权限规则配置：
```json
{
  "read": true,
  "write": "auth.openid == resource._openid"
}
```

**说明**：
- `read: true`：所有用户可读（用于排行榜等功能）
- `write: "auth.openid == resource._openid"`：只能修改自己的数据

### 3. 云函数部署

#### updateInviterReward 部署：
1. 右键点击 `cloudfunctions/updateInviterReward` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

#### getLeaderboard 部署：
1. 右键点击 `cloudfunctions/getLeaderboard` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

#### 验证部署：
在云开发控制台的"云函数"标签中确认看到两个函数

## 部署步骤

### 1. 配置云开发环境
- 在微信开发者工具中开通云开发
- 记录环境ID：`cloud1-9gx9hhw6416d5994`（已配置）

### 2. 创建数据库集合
- 在云开发控制台创建 `players` 集合
- 设置权限规则（见下方配置）

### 3. 部署云函数
需要部署以下2个云函数：
- `updateInviterReward`：邀请者奖励发放
- `getLeaderboard`：排行榜数据获取

### 4. 测试数据上传
- 运行游戏，系统会自动检测并上传200条模拟数据
- 或使用控制台命令：`await window.uploadMockData()`

### 5. 验证功能
- 测试邀请码验证和奖励发放
- 测试排行榜数据获取
- 测试个人数据同步

## 注意事项

1. **数据量限制**
   - 云数据库单次操作限制为20条记录
   - 大量数据需要分批处理

2. **权限管理**
   - 小程序端只能读取数据，不能修改其他用户数据
   - 敏感操作需要通过云函数处理

3. **错误处理**
   - 所有云数据库操作都有本地备用方案
   - 网络异常时自动回退到本地模拟数据

4. **性能优化**
   - 合理使用数据缓存
   - 避免频繁的数据库查询

## 故障排除

### 常见问题

1. **云数据库初始化失败**
   - 检查环境ID是否正确
   - 确认云开发服务已开通

2. **数据上传失败**
   - 检查网络连接
   - 确认数据格式正确

3. **权限错误**
   - 检查数据库权限设置
   - 确认用户登录状态

### 调试方法

1. 查看控制台日志
2. 使用云开发调试工具
3. 检查云函数日志

## 成本分析

### 云函数调用次数优化

#### 🎯 优化前 vs 优化后

| 操作类型 | 优化前 | 优化后 | 节省 |
|---------|--------|--------|------|
| 更新分数 | 云函数 | 本地操作 | ✅ 100% |
| 更新金币 | 云函数 | 本地操作 | ✅ 100% |
| 查询个人数据 | 云函数 | 本地操作 | ✅ 100% |
| 获取排行榜 | 本地计算 | 云函数 | ⚡ 性能提升 |
| 邀请者奖励 | 云函数 | 云函数 | - |

#### 📊 预估调用量（1000活跃用户/天）

**高频操作（现已本地化）**：
- 分数更新：~5000次/天 → 0次
- 金币更新：~3000次/天 → 0次
- 个人数据查询：~2000次/天 → 0次

**低频操作（仍使用云函数）**：
- 排行榜查询：~500次/天
- 邀请码使用：~50次/天

**总计**：从 10,550次/天 降至 550次/天，**节省95%云函数调用**

### 性能优势

1. **响应速度**：本地操作延迟 < 10ms，云函数延迟 100-500ms
2. **离线支持**：本地数据修改不依赖网络
3. **用户体验**：游戏操作即时响应，后台异步同步

## 后续扩展

1. **实时数据同步**
   - 使用云数据库实时推送功能
   - 实现排行榜实时更新

2. **数据分析**
   - 添加玩家行为统计
   - 游戏数据分析报表

3. **社交功能**
   - 好友系统
   - 公会/团队功能

4. **安全增强**
   - 数据加密
   - 防作弊机制
