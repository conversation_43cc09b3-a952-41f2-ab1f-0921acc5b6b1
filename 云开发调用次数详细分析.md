# 云开发调用次数详细分析

## 📊 当前所有调用点统计

### 1. 🕐 定时云函数（后台自动执行）

#### `updateLeaderboardCache` - 排行榜缓存更新
- **触发频率**：每5分钟一次
- **调用次数**：13次/5分钟
  - 1次云函数调用
  - 6次数据库聚合查询（每个关卡查询前100名）
  - 6次数据库更新操作（更新排行榜缓存）
- **调用时机**：自动定时执行，无需用户操作

---

### 2. 🎮 游戏初始化（用户进入游戏时）

#### `GameInitializer` → `GameDataManager.initialize()`
- **触发频率**：每次进入游戏一次
- **调用次数**：3次（已优化）
  - 1次 `login` 云函数调用（获取openid）
  - 1次数据库查询（获取用户数据）
  - 1次数据库更新（同步用户数据，如果需要）

#### `WeChatLoginManager.silentLogin()`
- **触发频率**：每次进入游戏一次
- **调用次数**：3次
  - 1次 `login` 云函数调用
  - 1次数据库查询（检查用户是否存在）
  - 1次数据库操作（创建或更新用户数据）

---

### 3. 🏆 游戏结束时（用户完成一局游戏）

#### `GameOverUI.syncGameEndData()`
- **触发频率**：每局游戏结束一次
- **调用次数**：最多6次（仅在有新纪录时）
  - 如果创造新纪录：
    - 1次 `login` 云函数调用
    - 1次数据库查询（获取用户数据）
    - 1次数据库更新（更新分数）
  - 总金币同步：
    - 1次 `login` 云函数调用
    - 1次数据库查询（获取用户数据）
    - 1次数据库更新（更新金币）

---

### 4. 📋 排行榜查看（用户主动查看）

#### `GlobalRankUI` - 全服排行榜
- **触发频率**：用户点击排行榜时
- **调用次数**：1次
  - 1次数据库查询（从缓存表读取排行榜数据）

#### `FriendsRankUI` - 好友排行榜
- **触发频率**：用户点击好友排行榜时
- **调用次数**：0次
  - 使用本地模拟数据，不调用云开发

---

### 5. 🎁 邀请码系统（用户使用邀请码时）

#### `InviteCodeManager.useInviteCode()`
- **触发频率**：用户输入邀请码时
- **调用次数**：4次
  - 1次数据库查询（验证邀请码是否存在）
  - 1次 `updateInviterReward` 云函数调用
  - 1次数据库查询（云函数内部查询邀请者）
  - 1次数据库更新（云函数内部更新邀请者金币）

#### `WeChatLoginManager.generateUniqueInviteCode()`
- **触发频率**：新用户首次登录时
- **调用次数**：3次
  - 1次 `generateInviteCode` 云函数调用
  - 1次数据库查询（云函数内部验证唯一性）
  - 1次数据库更新（云函数内部记录邀请码）

---

### 6. 🔧 测试和调试功能

#### `CloudDatabaseManager.testCloudFunction()`
- **触发频率**：仅开发测试时
- **调用次数**：1次
  - 1次 `getLeaderboard` 云函数调用

#### `WeChatFriendsData.initializeLeaderboardCache()`
- **触发频率**：仅在缓存缺失时
- **调用次数**：1次
  - 1次 `updateLeaderboardCache` 云函数调用（手动触发）

---

## 📈 实际使用场景调用频率分析

### 正常游戏流程：

1. **用户进入游戏**：6次调用
   - 游戏初始化：3次
   - 微信登录：3次

2. **用户玩一局游戏**：
   - 游戏进行中：0次调用 ✅
   - 游戏结束：最多6次调用（仅在新纪录时）

3. **用户查看排行榜**：1次调用
   - 从缓存表读取数据

4. **后台定时任务**：13次/5分钟
   - 排行榜缓存更新

### 高频使用场景预估：

假设有100个活跃用户，每天平均：
- 每人进入游戏2次：100 × 2 × 6 = 1,200次
- 每人玩10局游戏，其中1局有新纪录：100 × 1 × 6 = 600次
- 每人查看排行榜3次：100 × 3 × 1 = 300次
- 定时任务：24小时 × 12次/小时 × 13次 = 3,744次

**每日总计约：5,844次调用**

---

## 🎯 优化效果总结

### 优化前问题：
- 初始化循环调用：21次
- 游戏中频繁同步：每收集1金币 = 3次
- 定时任务过频：每分钟13次

### 优化后效果：
- 初始化：减少到6次（减少71%）
- 游戏中：0次调用（减少100%）
- 定时任务：每5分钟13次（减少80%）

**整体预计减少85%以上的调用量！**
